<template>
  <div class="drone-dashboard">
    <!-- 顶部标题栏 -->
    <header class="header">
      <h1>无人机可视化大屏</h1>
      <div class="header-nav">
        <span class="nav-item active">飞行状态</span>
        <span class="nav-item">设备</span>
        <span class="nav-item">设置</span>
      </div>
      <div class="time">16:37:36<br>2023/06/08</div>
    </header>

    <div class="main-content">
      <!-- 左侧面板 -->
      <aside class="left-panel">
        <!-- 设备详情 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="drone-icon">�</div>
            <h3>设备详情</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="device-info">
            <div class="device-item">
              <span>• 设备编号：0001</span>
            </div>
            <div class="device-item">
              <span>• 设备状态：在线</span>
            </div>
            <div class="device-item">
              <span>• 飞行模式：自动</span>
            </div>
            <div class="device-item">
              <span>• 电池电量：85%</span>
            </div>
          </div>
        </div>

        <!-- 作业列表 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="list-icon">📋</div>
            <h3>作业列表</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="job-stats">
            <div class="stat-row">
              <span class="stat-label">作业总数</span>
              <span class="stat-value">12</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">作业成功率</span>
              <span class="stat-value">95.8%</span>
            </div>
          </div>
        </div>

        <!-- 单架日志 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="log-icon">📝</div>
            <h3>单架日志</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="log-list">
            <div class="log-item">
              <span class="log-level">警告</span>
              <span class="log-content">高度传感器异常</span>
              <span class="log-time">2023/07/18 14:31</span>
            </div>
            <div class="log-item">
              <span class="log-level">信息</span>
              <span class="log-content">高度传感器异常</span>
              <span class="log-time">2023/07/18 14:31</span>
            </div>
          </div>
        </div>

        <!-- 天气情况 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="weather-icon-header">🌤️</div>
            <h3>天气情况</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="weather-grid">
            <div class="weather-item">
              <div class="weather-icon">☀️</div>
              <div class="weather-value">25°</div>
              <div class="weather-label">温度</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">💨</div>
              <div class="weather-value">4级</div>
              <div class="weather-label">风力</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">💧</div>
              <div class="weather-value">65%</div>
              <div class="weather-label">湿度</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">🌡️</div>
              <div class="weather-value">70%</div>
              <div class="weather-label">气压</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">👁️</div>
              <div class="weather-value">5km</div>
              <div class="weather-label">能见度</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">🌙</div>
              <div class="weather-value">晴朗</div>
              <div class="weather-label">天气</div>
            </div>
          </div>
        </div>
      </aside>

      <!-- 中央地图区域 -->
      <main class="map-container">
        <div class="map-view">
          <div class="map-background">
            <!-- 地图控制按钮 -->
            <div class="map-controls">
              <div class="control-group">
                <span>地图</span>
                <span>卫星图</span>
                <span>混合</span>
                <span>3D</span>
                <span>街景</span>
              </div>
            </div>
            <!-- 指南针 -->
            <div class="compass">
              <div class="compass-inner">
                <div class="compass-needle">N</div>
              </div>
            </div>
          </div>
          <div class="drone-marker">🚁</div>
        </div>

        <!-- 底部控制面板 -->
        <div class="bottom-control-panel">
          <!-- 天气情况 -->
          <div class="weather-section">
            <div class="section-title">🌤️ 天气情况</div>
            <div class="weather-items">
              <div class="weather-item-small">
                <div class="weather-icon">☀️</div>
                <div class="weather-text">晴朗<br>25°</div>
              </div>
              <div class="weather-item-small">
                <div class="weather-icon">💨</div>
                <div class="weather-text">4级<br>风力</div>
              </div>
              <div class="weather-item-small">
                <div class="weather-icon">💧</div>
                <div class="weather-text">65%<br>湿度</div>
              </div>
              <div class="weather-item-small">
                <div class="weather-icon">🌡️</div>
                <div class="weather-text">70%<br>气压</div>
              </div>
              <div class="weather-item-small">
                <div class="weather-icon">👁️</div>
                <div class="weather-text">5km<br>能见度</div>
              </div>
              <div class="weather-item-small">
                <div class="weather-icon">🌙</div>
                <div class="weather-text">晴朗<br>天气</div>
              </div>
            </div>
          </div>

          <!-- 飞行控制 -->
          <div class="flight-control">
            <div class="section-title">🎮 飞行控制</div>
            <div class="joystick-container">
              <div class="circular-joystick">
                <div class="joystick-outer-ring">
                  <button class="direction-btn up"></button>
                  <button class="direction-btn right"></button>
                  <button class="direction-btn down"></button>
                  <button class="direction-btn left"></button>
                  <div class="joystick-center-circle"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 执行操作 -->
          <div class="action-section">
            <div class="section-title">🎯 执行操作</div>
            <div class="action-grid">
              <button class="action-btn">起飞</button>
              <button class="action-btn">降落</button>
              <button class="action-btn">悬停</button>
              <button class="action-btn">返航</button>
            </div>
            <div class="section-title">📷 拍摄操作</div>
            <div class="action-grid">
              <button class="action-btn">拍照</button>
              <button class="action-btn">录像</button>
            </div>
          </div>
        </div>
      </main>

      <!-- 右侧面板 -->
      <aside class="right-panel">
        <!-- 当前飞机参数 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="plane-icon">✈️</div>
            <h3>当前飞机参数</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="params-grid">
            <div class="param-item">
              <span class="param-label">VelCmps</span>
              <span class="param-value">Stabilized</span>
            </div>
            <div class="param-item">
              <span class="param-label">高度(m)</span>
              <span class="param-value">120.0</span>
            </div>
            <div class="param-item">
              <span class="param-label">速度(m/s)</span>
              <span class="param-value">15.2</span>
            </div>
            <div class="param-item">
              <span class="param-label">航向角</span>
              <span class="param-value">0.0</span>
            </div>
            <div class="param-item">
              <span class="param-label">俯仰角</span>
              <span class="param-value">-0.0009</span>
            </div>
            <div class="param-item">
              <span class="param-label">横滚角</span>
              <span class="param-value">-2.328</span>
            </div>
            <div class="param-item">
              <span class="param-label">电池电量</span>
              <span class="param-value">85%</span>
            </div>
          </div>
        </div>

        <!-- 主要功能 -->
        <div class="panel-section">
          <div class="section-header">
            <div class="function-icon">⚙️</div>
            <h3>主要功能</h3>
            <div class="expand-icon">></div>
          </div>
          <div class="function-buttons">
            <button class="func-btn">航线规划</button>
            <button class="func-btn">实时监控</button>
            <button class="func-btn">数据分析</button>
            <button class="func-btn">设备管理</button>
            <button class="func-btn">系统设置</button>
            <button class="func-btn">报告导出</button>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="panel-section">
          <div class="detail-info">
            <p>GPS 1 DGPS: 0.000P 2.343T 纬度: 0.000</p>
            <p>高度 (1.000000P): 1.000000P</p>
            <p>速度 (1.000000P): 1.000000P</p>
            <p>航向角 (1.000000P): 1.000000P</p>
            <p>俯仰角 (1.000000P): 1.000000P</p>
            <p>横滚角 (1.000000P): 1.000000P</p>
            <p>电池电量 (1.000000P): 1.000000P</p>
            <p>1 航点距离 (1.000000P): 1.000000P</p>
            <p>1 航点方位角 (1.000000P): 1.000000P</p>
            <p>1 航点高度 (1.000000P): 1.000000P</p>
            <p>1 航点速度 (1.000000P): 1.000000P</p>
            <p>1 航点航向角 (1.000000P): 1.000000P</p>
            <p>1 航点俯仰角 (1.000000P): 1.000000P</p>
            <p>1 航点横滚角 (1.000000P): 1.000000P</p>
            <p>1 航点电池电量 (1.000000P): 1.000000P</p>
            <p>1 航点距离 (1.000000P): 1.000000P</p>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DroneDashboard',
  data() {
    return {
      // 静态数据
    }
  }
}
</script>

<style scoped>
.drone-dashboard {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a1428 0%, #1a2f5a 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
  overflow: hidden;
  font-size: 12px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid #1e4a73;
  height: 50px;
}

.header h1 {
  font-size: 18px;
  margin: 0;
  color: #4a9eff;
  font-weight: normal;
}

.header-nav {
  display: flex;
  gap: 20px;
}

.nav-item {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.3s;
  font-size: 12px;
}

.nav-item.active {
  background: #4a9eff;
  color: #000;
}

.time {
  text-align: right;
  font-size: 11px;
  line-height: 1.3;
  color: #a0c4ff;
}

.main-content {
  display: flex;
  height: calc(100vh - 50px);
  gap: 12px;
  padding: 12px;
}

.left-panel, .right-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.panel-section {
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid #1e4a73;
  border-radius: 4px;
  padding: 12px;
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #1e4a73;
}

.section-header h3 {
  margin: 0;
  color: #4a9eff;
  font-size: 14px;
  font-weight: normal;
  flex: 1;
}

.expand-icon {
  color: #4a9eff;
  font-size: 14px;
}

.device-info .device-item {
  margin-bottom: 6px;
  font-size: 13px;
  color: #a0c4ff;
}

.job-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.stat-label {
  color: #a0c4ff;
}

.stat-value {
  color: #4a9eff;
  font-weight: bold;
}

.log-list {
  max-height: 100px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  font-size: 12px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.log-level {
  color: #ff6b6b;
  font-weight: bold;
}

.log-content {
  color: #a0c4ff;
  margin: 3px 0;
}

.log-time {
  color: #666;
  font-size: 11px;
}

.weather-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.weather-item {
  text-align: center;
  padding: 8px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(74, 158, 255, 0.2);
}

.weather-icon {
  font-size: 16px;
  margin-bottom: 4px;
}

.weather-value {
  font-size: 13px;
  font-weight: bold;
  color: #4a9eff;
}

.weather-label {
  font-size: 11px;
  color: #a0c4ff;
}

.map-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.map-view {
  flex: 1;
  position: relative;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid #1e4a73;
  border-radius: 4px;
  overflow: hidden;
}

.map-background {
  width: 100%;
  height: 100%;
  position: relative;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600"><defs><pattern id="water" patternUnits="userSpaceOnUse" width="4" height="4"><rect width="4" height="4" fill="%23326ba8"/><circle cx="2" cy="2" r="0.5" fill="%234a9eff" opacity="0.3"/></pattern></defs><rect width="800" height="600" fill="%23326ba8"/><path d="M0,300 Q200,250 400,300 T800,300 L800,600 L0,600 Z" fill="url(%23water)"/><rect x="100" y="100" width="600" height="200" fill="%232d5aa0" opacity="0.8"/><rect x="150" y="120" width="500" height="160" fill="%231e4a73" opacity="0.6"/></svg>') center/cover;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 4px;
}

.control-group {
  display: flex;
  gap: 8px;
}

.control-group span {
  padding: 4px 8px;
  background: rgba(74, 158, 255, 0.2);
  border: 1px solid #4a9eff;
  border-radius: 2px;
  font-size: 10px;
  color: #4a9eff;
  cursor: pointer;
}

.compass {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #4a9eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compass-inner {
  width: 40px;
  height: 40px;
  border: 1px solid #4a9eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(74, 158, 255, 0.2) 0%, transparent 70%);
}

.compass-needle {
  color: #4a9eff;
  font-weight: bold;
  font-size: 14px;
}

.drone-marker {
  position: absolute;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  animation: pulse 2s infinite;
  z-index: 10;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.bottom-control-panel {
  display: flex;
  gap: 12px;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid #1e4a73;
  border-radius: 4px;
  padding: 12px;
  height: 140px;
}

.weather-section {
  flex: 2;
}

.section-title {
  font-size: 13px;
  color: #4a9eff;
  margin-bottom: 8px;
  border-bottom: 1px solid #1e4a73;
  padding-bottom: 4px;
}

.weather-items {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 6px;
}

.weather-item-small {
  text-align: center;
  padding: 6px;
  background: rgba(74, 158, 255, 0.1);
  border-radius: 3px;
  border: 1px solid rgba(74, 158, 255, 0.2);
}

.weather-item-small .weather-icon {
  font-size: 14px;
  margin-bottom: 3px;
}

.weather-item-small .weather-text {
  font-size: 11px;
  color: #a0c4ff;
  line-height: 1.3;
}

.flight-control {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.joystick-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circular-joystick {
  width: 90px;
  height: 90px;
  position: relative;
}

.joystick-outer-ring {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #4a9eff;
  position: relative;
}

.direction-btn {
  position: absolute;
  width: 16px;
  height: 16px;
  background: #4a9eff;
  border: none;
  color: #fff;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.direction-btn.up {
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.direction-btn.right {
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  clip-path: polygon(0% 0%, 0% 100%, 100% 50%);
}

.direction-btn.down {
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  clip-path: polygon(0% 0%, 50% 100%, 100% 0%);
}

.direction-btn.left {
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  clip-path: polygon(100% 0%, 0% 50%, 100% 100%);
}

.direction-btn:hover {
  background: #6bb6ff;
}

.joystick-center-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #000;
}

.action-section {
  flex: 1;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.action-btn {
  padding: 4px 8px;
  background: linear-gradient(45deg, #4a9eff, #326ba8);
  border: none;
  border-radius: 2px;
  color: #fff;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(74, 158, 255, 0.3);
}

.params-grid {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.param-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid rgba(74, 158, 255, 0.1);
  font-size: 12px;
}

.param-label {
  color: #a0c4ff;
}

.param-value {
  color: #4a9eff;
  font-weight: bold;
}

.function-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.func-btn {
  padding: 8px 10px;
  background: rgba(74, 158, 255, 0.2);
  border: 1px solid #4a9eff;
  border-radius: 3px;
  color: #4a9eff;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 12px;
}

.func-btn:hover {
  background: rgba(74, 158, 255, 0.4);
  color: #fff;
}

.detail-info {
  font-size: 11px;
  color: #a0c4ff;
  line-height: 1.5;
  max-height: 250px;
  overflow-y: auto;
}

.detail-info p {
  margin: 3px 0;
  padding: 3px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: #4a9eff;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6bb6ff;
}
</style>