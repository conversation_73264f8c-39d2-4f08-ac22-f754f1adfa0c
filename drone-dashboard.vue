<template>
  <div class="drone-dashboard">
    <!-- 顶部标题栏 -->
    <header class="header">
      <h1>无人机可视化大屏</h1>
      <div class="header-nav">
        <span class="nav-item active">飞行状态</span>
        <span class="nav-item">设备</span>
        <span class="nav-item">设置</span>
      </div>
      <div class="time">16:37:36<br>2023/06/08</div>
    </header>

    <div class="main-content">
      <!-- 左侧面板 -->
      <aside class="left-panel">
        <!-- 设备详情 -->
        <div class="panel-section">
          <h3>🔧 设备详情</h3>
          <div class="device-info">
            <div class="device-item">
              <span>设备编号：</span>
              <span>0001</span>
            </div>
            <div class="device-item">
              <span>设备状态：</span>
              <span class="status-online">在线</span>
            </div>
            <div class="device-item">
              <span>飞行模式：</span>
              <span>自动</span>
            </div>
            <div class="device-item">
              <span>电池电量：</span>
              <span>85%</span>
            </div>
          </div>
        </div>

        <!-- 作业列表 -->
        <div class="panel-section">
          <h3>📋 作业列表</h3>
          <div class="job-list">
            <div class="job-stats">
              <div class="stat-item">
                <span class="stat-label">作业总数</span>
                <span class="stat-value">12</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">作业成功率</span>
                <span class="stat-value">95.8%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 单架日志 -->
        <div class="panel-section">
          <h3>📝 单架日志</h3>
          <div class="log-list">
            <div class="log-item">
              <span class="log-time">16:35</span>
              <span class="log-content">起飞准备完成</span>
            </div>
            <div class="log-item">
              <span class="log-time">16:30</span>
              <span class="log-content">系统自检完成</span>
            </div>
          </div>
        </div>

        <!-- 天气情况 -->
        <div class="panel-section">
          <h3>🌤️ 天气情况</h3>
          <div class="weather-grid">
            <div class="weather-item">
              <div class="weather-icon">☀️</div>
              <div class="weather-value">25°</div>
              <div class="weather-label">温度</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">💨</div>
              <div class="weather-value">4级</div>
              <div class="weather-label">风力</div>
            </div>
            <div class="weather-item">
              <div class="weather-icon">💧</div>
              <div class="weather-value">65%</div>
              <div class="weather-label">湿度</div>
            </div>
          </div>
        </div>
      </aside>

      <!-- 中央地图区域 -->
      <main class="map-container">
        <div class="map-view">
          <div class="map-placeholder">
            <div class="map-grid"></div>
            <div class="map-overlay">地图视图</div>
          </div>
          <div class="drone-marker">🚁</div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
          <div class="direction-control">
            <h4>🎮 飞行控制</h4>
            <div class="joystick">
              <button class="direction-btn up">▲</button>
              <div class="joystick-center">
                <button class="direction-btn left">◀</button>
                <div class="center-dot"></div>
                <button class="direction-btn right">▶</button>
              </div>
              <button class="direction-btn down">▼</button>
            </div>
          </div>
          
          <div class="action-buttons">
            <h4>🎯 执行操作</h4>
            <div class="button-grid">
              <button class="action-btn">起飞</button>
              <button class="action-btn">降落</button>
              <button class="action-btn">悬停</button>
              <button class="action-btn">返航</button>
              <button class="action-btn">拍照</button>
              <button class="action-btn">录像</button>
            </div>
          </div>
        </div>
      </main>

      <!-- 右侧面板 -->
      <aside class="right-panel">
        <!-- 当前飞机参数 -->
        <div class="panel-section">
          <h3>✈️ 当前飞机参数</h3>
          <div class="params-grid">
            <div class="param-row">
              <span>经度(Lng)</span>
              <span>113.2644</span>
            </div>
            <div class="param-row">
              <span>纬度(Lat)</span>
              <span>23.1291</span>
            </div>
            <div class="param-row">
              <span>高度</span>
              <span>120m</span>
            </div>
            <div class="param-row">
              <span>速度</span>
              <span>15.2m/s</span>
            </div>
            <div class="param-row">
              <span>电池</span>
              <span>85%</span>
            </div>
          </div>
        </div>

        <!-- 主要功能 -->
        <div class="panel-section">
          <h3>⚙️ 主要功能</h3>
          <div class="function-buttons">
            <button class="func-btn">航线规划</button>
            <button class="func-btn">实时监控</button>
            <button class="func-btn">数据分析</button>
            <button class="func-btn">设备管理</button>
            <button class="func-btn">系统设置</button>
            <button class="func-btn">报告导出</button>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DroneDashboard',
  data() {
    return {
      // 静态数据
    }
  }
}
</script>

<style scoped>
.drone-dashboard {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1445 0%, #1a2980 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid #00d4ff;
}

.header h1 {
  font-size: 24px;
  margin: 0;
  color: #00d4ff;
}

.header-nav {
  display: flex;
  gap: 30px;
}

.nav-item {
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item.active {
  background: #00d4ff;
  color: #000;
}

.time {
  text-align: right;
  font-size: 14px;
  line-height: 1.4;
}

.main-content {
  display: flex;
  height: calc(100vh - 80px);
  gap: 15px;
  padding: 15px;
}

.left-panel, .right-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.panel-section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 15px;
}

.panel-section h3 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  font-size: 16px;
  border-bottom: 1px solid #00d4ff;
  padding-bottom: 8px;
}

.device-info .device-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.status-online {
  color: #00ff00;
}

.job-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #ccc;
}

.stat-value {
  display: block;
  font-size: 18px;
  color: #00d4ff;
  font-weight: bold;
}

.log-list {
  max-height: 120px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 12px;
}

.log-time {
  color: #00d4ff;
  min-width: 40px;
}

.weather-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.weather-item {
  text-align: center;
  padding: 10px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 4px;
}

.weather-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.weather-value {
  font-size: 16px;
  font-weight: bold;
  color: #00d4ff;
}

.weather-label {
  font-size: 12px;
  color: #ccc;
}

.map-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.map-view {
  flex: 1;
  position: relative;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  overflow: hidden;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(45deg, #1a2980 25%, #26d0ce 25%, #26d0ce 50%, #1a2980 50%, #1a2980 75%, #26d0ce 75%);
  background-size: 40px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.3) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.3) 1px, transparent 1px);
  background-size: 50px 50px;
}

.map-overlay {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.drone-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.control-panel {
  display: flex;
  gap: 20px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  padding: 20px;
}

.direction-control {
  flex: 1;
}

.direction-control h4, .action-buttons h4 {
  margin: 0 0 15px 0;
  color: #00d4ff;
}

.joystick {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.joystick-center {
  display: flex;
  align-items: center;
  gap: 5px;
}

.direction-btn {
  width: 40px;
  height: 40px;
  background: #00d4ff;
  border: none;
  border-radius: 50%;
  color: #000;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.direction-btn:hover {
  background: #00a8cc;
  transform: scale(1.1);
}

.center-dot {
  width: 20px;
  height: 20px;
  background: #ff6b6b;
  border-radius: 50%;
}

.action-buttons {
  flex: 1;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.action-btn {
  padding: 10px;
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  border: none;
  border-radius: 4px;
  color: #000;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 212, 255, 0.3);
}

.params-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  font-size: 14px;
}

.function-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.func-btn {
  padding: 8px 12px;
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid #00d4ff;
  border-radius: 4px;
  color: #00d4ff;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 12px;
}

.func-btn:hover {
  background: rgba(0, 212, 255, 0.4);
  color: #fff;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: #00d4ff;
  border-radius: 3px;
}
</style>